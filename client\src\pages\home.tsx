import { useTranslation } from 'react-i18next';
import { Image } from 'lucide-react';
import { LanguageSelector } from '@/components/language-selector';
import { UploadArea } from '@/components/upload-area';
import { FeatureSection } from '@/components/feature-section';
import { GallerySection } from '@/components/gallery-section';
import { TestimonialsSection } from '@/components/testimonials-section';

import { Footer } from '@/components/footer';
import { SEOHead } from '@/components/seo-head';

const HowItWorksSection = () => {
  const { t } = useTranslation();

  const steps = [
    { key: 'upload', number: 1, gradient: 'from-primary to-primary/80' },
    { key: 'process', number: 2, gradient: 'from-secondary to-secondary/80' },
    { key: 'download', number: 3, gradient: 'from-accent to-accent/80' },
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-white gradient-mesh">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-slide-up opacity-0">
          <h2 className="text-4xl font-bold text-gray-900 mb-6">
            {t('howItWorks.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('howItWorks.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {steps.map((step, index) => (
            <div key={step.key} className={`text-center animate-slide-up animate-delay-${(index + 1) * 100} opacity-0`}>
              <div className={`w-20 h-20 mx-auto mb-6 bg-gradient-to-r ${step.gradient} rounded-full flex items-center justify-center text-white text-2xl font-bold interactive-hover animate-scale-in`}>
                {step.number}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                {t(`howItWorks.steps.${step.key}.title`)}
              </h3>
              <p className="text-gray-600">
                {t(`howItWorks.steps.${step.key}.description`)}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default function Home() {
  const { t } = useTranslation();

  return (
    <>
      <SEOHead />
      
      {/* Header */}
      <header className="glass-card shadow-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-2 animate-slide-left">
              <div className="w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center animate-pulse">
                <Image className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-2xl font-bold text-gray-900">
                {t('header.title')}
              </h1>
            </div>
            <nav className="hidden md:flex items-center space-x-8 animate-slide-right">
              <a href="#features" className="text-gray-600 hover:text-primary transition-colors">
                {t('nav.features')}
              </a>
              <a href="#how-it-works" className="text-gray-600 hover:text-primary transition-colors">
                {t('nav.howItWorks')}
              </a>
              <a href="#testimonials" className="text-gray-600 hover:text-primary transition-colors">
                {t('nav.testimonials')}
              </a>
              <LanguageSelector />
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="gradient-hero py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 hero-title">
              {t('hero.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8 hero-subtitle">
              {t('hero.subtitle')}
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-2 text-secondary" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                </svg>
                <span>{t('hero.features.free')}</span>
              </span>
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-2 text-secondary" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                </svg>
                <span>{t('hero.features.noSignup')}</span>
              </span>
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-2 text-secondary" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                </svg>
                <span>{t('hero.features.instant')}</span>
              </span>
            </div>
          </div>

          <UploadArea />
        </div>
      </section>

      {/* Features Section */}
      <section id="features">
        <FeatureSection />
      </section>

      {/* Gallery Section */}
      <GallerySection />

      {/* How It Works Section */}
      <section id="how-it-works">
        <HowItWorksSection />
      </section>

      {/* Testimonials Section */}
      <section id="testimonials">
        <TestimonialsSection />
      </section>



      {/* Footer */}
      <Footer />
    </>
  );
}
